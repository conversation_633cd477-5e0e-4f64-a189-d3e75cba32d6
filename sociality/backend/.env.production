# Production Environment Variables for Backend
# These should be set in Railway environment variables

PORT=5000
NODE_ENV=production

# Database Configuration
MONGO_URI=

# JWT Configuration
JWT_SECRET=

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=
CLOUDINARY_API_KEY=
CLOUDINARY_API_SECRET=

# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
SESSION_SECRET=

# Frontend URL (Vercel deployment)
FRONTEND_URL=
# Telegram Bot Configuration (Optional)
TELEGRAM_BOT_TOKEN=
TELEGRAM_BOT_USERNAME=

# Discord Bot Configuration (Optional)
DISCORD_BOT_TOKEN=
DISCORD_CLIENT_ID=

# Federation Configuration (Disabled for production)
FEDERATION_ENABLED=false
ENABLE_CROSS_PLATFORM=false

# Logging Configuration
LOG_LEVEL=error
ENABLE_SOCKET_LOGS=false
