{"version": 2, "builds": [{"src": "backend/server.js", "use": "@vercel/node", "config": {"maxDuration": 30, "memory": 1024}}], "env": {"SERVE_FRONTEND": "true", "NODE_ENV": "production"}, "rewrites": [{"source": "/api/(.*)", "destination": "/backend/server.js"}, {"source": "/socket.io/(.*)", "destination": "/backend/server.js"}, {"source": "/(.*)", "destination": "/backend/server.js"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://sociality-black.vercel.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, <PERSON>ie"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}, {"source": "/socket.io/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://sociality-black.vercel.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, <PERSON>ie"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}]}]}