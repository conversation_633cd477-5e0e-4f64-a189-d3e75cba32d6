{"version": 3, "sources": ["../src/index.ts", "../src/utils/asArray.ts", "../src/utils/is.ts", "../src/utils/joinTruthy.ts", "../src/utils/stableHash.ts", "../src/utils/stringManipulators.ts", "../src/Rule.ts", "../src/Sheet.ts", "../src/utils/forIn.ts", "../src/cx.ts"], "names": ["selectors", "classes", "styles", "name"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,SAAS,QAAW,GAAiB;AAC1C,SAAO,CAAC,EAAE,OAAO,CAAkB;AACrC;;;ACAO,SAAS,iBAAiB,UAA2B;AAC1D,SAAO,SAAS,WAAW,GAAG;AAChC;AAEO,SAAS,iBAAiB,UAA2B;AAC1D,SACE,SAAS,QAAQ,MAChB,aAAa,OACX,SAAS,SAAS,KAAK,SAAS,SAAS,SAAS,MAAM,GAAG,CAAC,CAAC,KAC9D,yBAAyB,QAAQ;AAEvC;AAEO,SAAS,gBACd,UACA,OACiB;AACjB,UACG,SAAS,KAAK,KAAK,OAAO,UAAU,aACrC,CAAC,eAAe,QAAQ,KACxB,CAAC,iBAAiB,QAAQ,KAC1B,CAAC,aAAa,QAAQ;AAE1B;AAEO,SAAS,aAAa,UAA2B;AACtD,SAAO,SAAS,WAAW,QAAQ;AACrC;AAEO,SAAS,cAAc,UAA2B;AACvD,SAAO,aAAa;AACtB;AAEO,SAAS,eAAe,UAA2B;AACxD,SAAO,aAAa;AACtB;AAEO,SAAS,SAAS,OAAiC;AACxD,SAAO,QAAQ,OAAO;AACxB;AAMO,SAAS,yBACd,OACuB;AACvB,SAAO,SAAS,KAAK,MAAM,MAAM,WAAW,GAAG,KAAK,iBAAiB,KAAK;AAC5E;;;ACnDO,SAAS,WAAW,KAAgB,YAAoB,IAAY;AACzE,SAAO,IAAI,OAAO,OAAO,EAAE,KAAK,SAAS;AAC3C;;;ACDO,SAAS,WAAW,QAAgB,MAAsB;AAC/D,MAAI,OAAO;AACX,MAAI,KAAK,WAAW;AAAG,WAAO,KAAK,SAAS;AAC5C,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,OAAO,KAAK,WAAW,CAAC;AAC9B,YAAQ,QAAQ,KAAK,OAAO;AAC5B,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,GAAG,UAAU,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;AAC/C;;;ACTO,SAAS,oBAAoB,UAAkB,OAAuB;AAC3E,MAAI,aAAa,WAAW;AAC1B,WAAO,IAAI,KAAK;AAAA,EAClB;AAEA,SAAO;AACT;AAEO,SAAS,gBAAgB,KAAqB;AACnD,SAAO,IAAI,QAAQ,mBAAmB,OAAO,EAAE,YAAY;AAC7D;AAEO,SAAS,eAAe,UAAkB,OAAuB;AACtE,SAAO,GAAG,QAAQ,IAAI,KAAK;AAC7B;AAEO,SAAS,QAAQ,KAAqB;AAC3C,SAAO,MAAM,IAAI,GAAG,KAAK;AAC3B;AAEO,SAAS,aAAa,MAAc,MAAsB;AAC/D,SAAO,OAAO,GAAG,IAAI;AAAA,EAAK,IAAI,KAAK;AACrC;;;ACXO,IAAM,OAAN,MAAM,MAAK;AAAA,EAKhB,YACU,OACD,UACA,OACC,UACR;AAJQ;AACD;AACA;AACC;AAER,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,SAAS,eAAe,UAAU,KAAK;AAC5C,UAAM,mBAAmB,KAAK,SAAS,cAAc;AAAA,MACnD,KAAK,SAAS;AAAA,IAChB;AACA,SAAK,OAAO,KAAK,SAAS,gBACrB,KAAK,SAAS,iBACf,WAAW,KAAK,MAAM,MAAM,KAAK,MAAM;AAC3C,SAAK,MAAM,WAAW,CAAC,KAAK,QAAQ,kBAAkB,KAAK,IAAI,CAAC;AAAA,EAClE;AAAA,EAEO,WAAmB;AACxB,QAAI,YAAY,eAAe,KAAK,SAAS,eAAe;AAAA,MAC1D,OAAO,KAAK;AAAA,IACd,CAAC;AAED,gBAAY,eAAe,KAAK,SAAS,gBAAgB;AAAA,MACvD,MAAM;AAAA,IACR,CAAC;AAED,WAAO,GAAG,SAAS,KAAK,MAAK,QAAQ,KAAK,UAAU,KAAK,KAAK,CAAC;AAAA,EACjE;AAAA,EAEA,OAAO,QAAQ,UAAkB,OAAuB;AACtD,UAAM,sBAAsB,gBAAgB,QAAQ;AACpD,WACE;AAAA,MACE;AAAA,MACA,oBAAoB,UAAU,KAAK;AAAA,IACrC,IAAI;AAAA,EAER;AACF;AAEO,SAAS,eACd,WACA,EAAE,OAAO,IAAI,QAAQ,GAAG,IAAuC,CAAC,GACxD;AACR,QAAM,SAAS,UAAU,OAAO,CAACA,YAAW,YAAY;AACtD,QAAI,iBAAiB,OAAO,GAAG;AAC7B,aAAOA,aAAY;AAAA,IACrB;AAEA,QAAI,yBAAyB,OAAO,GAAG;AACrC,aAAOA,aAAY,QAAQ,MAAM,CAAC;AAAA,IACpC;AAEA,WAAO,WAAW,CAACA,YAAW,OAAO,GAAG,GAAG;AAAA,EAG7C,GAAG,IAAI;AAGP,SAAO,WAAW,CAAC,QAAQ,QAAQ,KAAK,CAAC,GAAG,GAAG;AACjD;AAEO,IAAM,WAAN,MAAM,UAAS;AAAA,EAMpB,YACU,OACR,YAA2B,MAC3B;AAAA,IACE;AAAA,IACA;AAAA,EACF,IAGI,CAAC,GACL;AATQ;AANV,SAAO,gBAA0B,CAAC;AAClC,SAAO,iBAAgC;AACvC,SAAO,YAA2B;AAClC,SAAO,iBAA2B,CAAC;AAajC,SAAK,gBAAgB,gBAAgB,QAAQ,aAAa,IAAI,CAAC;AAC/D,SAAK,iBAAiB,iBAAiB,QAAQ,cAAc,IAAI,CAAC;AAClE,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EAEQ,SAAS,WAAoC;AACnD,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,YAAY;AACjB,WAAK,iBAAiB;AAAA,QACpB,KAAK,MAAM;AAAA;AAAA,QAEX,YAAY,KAAK,MAAM;AAAA,MACzB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,IAAI,gBAAyB;AAC3B,WAAO,KAAK,cAAc,SAAS,KAAK,KAAK,eAAe,SAAS;AAAA,EACvE;AAAA,EAEA,SAAS,WAA6B;AACpC,WAAO,IAAI,UAAS,KAAK,OAAO,WAAW;AAAA,MACzC,eAAe,KAAK;AAAA,MACpB,gBAAgB,KAAK;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EAEA,gBAAgB,cAAgC;AAC9C,WAAO,IAAI,UAAS,KAAK,OAAO,KAAK,gBAAgB;AAAA,MACnD,gBAAgB,KAAK;AAAA,MACrB,eAAe,KAAK,cAAc,OAAO,YAAY;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EAEA,iBAAiB,eAAiC;AAChD,WAAO,IAAI,UAAS,KAAK,OAAO,KAAK,gBAAgB;AAAA,MACnD,eAAe,KAAK;AAAA,MACpB,gBAAgB,KAAK,eAAe,OAAO,aAAa;AAAA,IAC1D,CAAC;AAAA,EACH;AAAA,EAEA,WAAW,UAAkB,OAAqB;AAChD,WAAO,IAAI,KAAK,KAAK,OAAO,UAAU,OAAO,IAAI;AAAA,EACnD;AACF;;;AC9IO,IAAM,QAAN,MAAY;AAAA,EAYjB,YACS,MACC,UACR;AAFO;AACC;AAVV;AAAA,SAAQ,eAA6B,CAAC;AAGtC;AAAA,SAAQ,gBAAwC,CAAC;AACjD,SAAQ,QAAgB;AACxB,SAAO,QAAQ;AAOb,SAAK,KAAK,WAAW,IAAI;AAEzB,SAAK,WAAW,KAAK,eAAe;AAAA,EACtC;AAAA,EAEA,WAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,OAAO,KAAmB;AACxB,SAAK,QAAQ,aAAa,KAAK,OAAO,GAAG;AAAA,EAC3C;AAAA,EAEA,QAAc;AACZ,SAAK;AAEL,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AAEA,SAAK,SAAS,YAAY,KAAK;AAAA,EACjC;AAAA,EAEA,YAAqB;AACnB,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EAEA,iBAA+C;AAE7C,QACE,OAAO,aAAa,eACpB,KAAK,UAAU;AAAA,IAEf,KAAK,aAAa,MAClB;AACA,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,WAAW,SAAS,cAAc,OAAO;AAC/C,aAAS,OAAO;AAChB,aAAS,KAAK,KAAK;AACnB,KAAC,KAAK,YAAY,SAAS,MAAM,YAAY,QAAQ;AACrD,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,MAAoB;AAC1B,UAAM,cAAc,KAAK,cAAc,KAAK,GAAG;AAE/C,QAAI,SAAS,WAAW,GAAG;AACzB,aAAO;AAAA,IACT;AAEA,SAAK,cAAc,KAAK,GAAG,IAAI,KAAK;AACpC,SAAK,aAAa,KAAK,IAAI,IAAI,CAAC,KAAK,UAAU,KAAK,KAAK;AAEzD,SAAK,OAAO,KAAK,SAAS,CAAC;AAC3B,WAAO,KAAK;AAAA,EACd;AACF;;;AC/EO,SAAS,MACd,KACA,IACM;AACN,aAAW,OAAO,KAAK;AACrB,OAAG,IAAI,KAAK,GAAG,IAAI,GAAG,CAAC;AAAA,EACzB;AACF;;;ACLO,SAAS,MAAM,MAAyB;AAC7C,QAAM,UAAU,KAAK,OAAO,CAACC,UAAmB,QAAQ;AACtD,QAAI,eAAe,KAAK;AACtB,MAAAA,SAAQ,KAAK,GAAG,GAAG;AAAA,IACrB,WAAW,OAAO,QAAQ,UAAU;AAClC,MAAAA,SAAQ,KAAK,GAAG;AAAA,IAClB,WAAW,MAAM,QAAQ,GAAG,GAAG;AAC7B,MAAAA,SAAQ,KAAK,GAAG,GAAG,GAAG,CAAC;AAAA,IACzB,WAAW,OAAO,QAAQ,UAAU;AAElC,aAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC5C,YAAI,OAAO;AACT,UAAAA,SAAQ,KAAK,GAAG;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAOA;AAAA,EACT,GAAG,CAAC,CAAa;AAEjB,SAAO,WAAW,SAAS,GAAG,EAAE,KAAK;AACvC;;;ATEO,SAAS,YACd,MACA,UACmB;AACnB,QAAM,QAAQ,IAAI,MAAM,MAAM,QAAQ;AAEtC,SAAO;AAAA,IACL;AAAA,IACA,UAAU,MAAM,SAAS,KAAK,KAAK;AAAA,IACnC,WAAW,MAAM,UAAU,KAAK,KAAK;AAAA,EACvC;AAEA,WAAS,OAAyB,QAA6B;AAC7D,UAAM,eAAgC,CAAC;AAEvC,yBAAqB,OAAO,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;AAAA,MACvD,CAAC,CAAC,WAAWC,SAAQ,QAAQ,MAAM;AACjC,sBAAc,OAAOA,SAAkB,QAAQ,EAAE;AAAA,UAC/C,CAAC,cAAc;AACb,2BAAe,WAAgB,SAAS;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAKA,UAAM,MAAM;AAEZ,WAAO;AAEP,aAAS,eAAeC,OAAS,WAAmB;AAClD,mBAAaA,KAA6B,IACxC,aAAaA,KAA6B,KAAK,oBAAI,IAAY;AACjE,mBAAaA,KAA6B,EAAE,IAAI,SAAS;AAAA,IAC3D;AAAA,EACF;AACF;AAIA,SAAS,qBACP,OACA,QACA,UACA;AACA,QAAM,SAA4C,CAAC;AAEnD,QAAM,QAAQ,CAAC,KAAa,UAAU;AACpC,QAAI,iBAAiB,GAAG,GAAG;AACzB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,SAAS,gBAAgB,GAAG;AAAA,MAC9B,EAAE,QAAQ,CAAC,SAAS,OAAO,KAAK,IAAI,CAAC;AAAA,IACvC;AAIA,WAAO,KAAK,CAAC,KAAK,OAAO,GAAG,GAAG,SAAS,SAAS,GAAG,CAAC,CAAC;AAAA,EACxD,CAAC;AAED,SAAO;AACT;AAEA,SAAS,cACP,OACA,QACA,UACU;AACV,QAAM,SAAmB,oBAAI,IAAY;AAEzC,QAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,QAAI,MAA8B,CAAC;AAGnC,QAAI,iBAAiB,QAAQ,GAAG;AAC9B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,SAAS,iBAAiB,QAAQ;AAAA,MACpC;AAAA,IAEF,WAAW,cAAc,QAAQ,GAAG;AAClC,YAAM,QAAQ,KAAoB;AAAA,IACpC,WAAW,aAAa,QAAQ,GAAG;AACjC,YAAM,iBAAiB,OAAO,OAAiB,UAAU,QAAQ;AAAA,IAGnE,WAAW,eAAe,QAAQ,GAAG;AACnC,YAAM,kBAAkB,OAAO,OAA6B,QAAQ;AAAA,IAGtE,WAAW,gBAAgB,UAAU,KAAK,GAAG;AAC3C,YAAM,OAAO,SAAS,WAAW,UAAU,KAAK;AAChD,YAAM,QAAQ,IAAI;AAClB,aAAO,IAAI,KAAK,IAAI;AAAA,IACtB;AAEA,WAAO,aAAa,KAAK,MAAM;AAAA,EACjC,CAAC;AAED,SAAO;AACT;AAEA,SAAS,aAAa,MAA8B,IAAiB;AACnE,OAAK,QAAQ,CAAC,cAAc,GAAG,IAAI,SAAS,CAAC;AAC7C,SAAO;AACT;AAGA,SAAS,kBACP,OACA,QACA,UACA;AACA,QAAM,UAAoB,oBAAI,IAAY;AAE1C,QAAM,YAAsB,CAAC;AAC7B,QAAM,QAAQ,CAAC,UAAkB,UAAU;AACzC,QAAI,gBAAgB,UAAU,KAAK,GAAG;AACpC,gBAAU,KAAK,KAAK,QAAQ,UAAU,KAAK,CAAC;AAC5C;AAAA,IACF;AACA,UAAM,MAAM,cAAc,OAAO,SAAS,CAAC,GAAG,QAAQ;AACtD,iBAAa,KAAK,OAAO;AAAA,EAC3B,CAAC;AAED,MAAI,CAAC,SAAS,gBAAgB;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,QAAQ;AACpB,UAAM,SAAS,UAAU,KAAK,GAAG;AACjC,UAAM;AAAA,MACJ,GAAG,eAAe,SAAS,eAAe;AAAA,QACxC,OAAO,SAAS;AAAA,MAClB,CAAC,CAAC,KAAK,MAAM;AAAA,IACf;AAAA,EACF;AAEA,UAAQ,IAAI,SAAS,cAAc;AACnC,SAAO;AACT;AAEA,SAAS,iBACP,OACA,QACA,YACA,UACA;AACA,QAAM,OAAO,aAAa,IAAI;AAI9B,QAAM,SAAS,cAAc,OAAO,QAAQ,QAAQ;AAEpD,QAAM,OAAO,GAAG;AAEhB,SAAO;AACT", "sourcesContent": ["import { Rule, Selector, mergeSelectors } from './Rule.js';\nimport { Sheet } from './Sheet.js';\nimport {\n  CSSVariablesObject,\n  ClassSet,\n  CreateSheetInput,\n  DirectClass,\n  ScopedStyles,\n  Styles,\n  createSheetReturn,\n} from './types.js';\nimport { asArray } from './utils/asArray.js';\nimport { forIn } from './utils/forIn.js';\nimport {\n  isCssVariables,\n  isDirectClass,\n  isMediaQuery,\n  isStyleCondition,\n  isValidProperty,\n} from './utils/is.js';\n\nexport { cx } from './cx.js';\n\nexport type { CreateSheetInput, Styles };\n\nexport function createSheet(\n  name: string,\n  rootNode?: HTMLElement | null,\n): createSheetReturn {\n  const sheet = new Sheet(name, rootNode);\n\n  return {\n    create,\n    getStyle: sheet.getStyle.bind(sheet),\n    isApplied: sheet.isApplied.bind(sheet),\n  };\n\n  function create<K extends string>(styles: CreateSheetInput<K>) {\n    const scopedStyles: ScopedStyles<K> = {} as ScopedStyles<K>;\n\n    iteratePreconditions(sheet, styles, new Selector(sheet)).forEach(\n      ([scopeName, styles, selector]) => {\n        iterateStyles(sheet, styles as Styles, selector).forEach(\n          (className) => {\n            addScopedStyle(scopeName as K, className);\n          },\n        );\n      },\n    );\n\n    // Commit the styles to the sheet.\n    // Done only once per create call.\n    // This way we do not update the DOM on every style.\n    sheet.apply();\n\n    return scopedStyles;\n\n    function addScopedStyle(name: K, className: string) {\n      scopedStyles[name as keyof ScopedStyles<K>] =\n        scopedStyles[name as keyof ScopedStyles<K>] ?? new Set<string>();\n      scopedStyles[name as keyof ScopedStyles<K>].add(className);\n    }\n  }\n}\n\n// This one plucks out all of the preconditions\n// and creates selector objects from them\nfunction iteratePreconditions(\n  sheet: Sheet,\n  styles: Styles,\n  selector: Selector,\n) {\n  const output: Array<[string, Styles, Selector]> = [];\n\n  forIn(styles, (key: string, value) => {\n    if (isStyleCondition(key)) {\n      return iteratePreconditions(\n        sheet,\n        value as Styles,\n        selector.addPrecondition(key),\n      ).forEach((item) => output.push(item));\n    }\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore - this is a valid case\n    output.push([key, styles[key], selector.addScope(key)]);\n  });\n\n  return output;\n}\n\nfunction iterateStyles(\n  sheet: Sheet,\n  styles: Styles,\n  selector: Selector,\n): ClassSet {\n  const output: ClassSet = new Set<string>();\n  // eslint-disable-next-line max-statements\n  forIn(styles, (property, value) => {\n    let res: string[] | Set<string> = [];\n\n    // Postconditions\n    if (isStyleCondition(property)) {\n      res = iterateStyles(\n        sheet,\n        value as Styles,\n        selector.addPostcondition(property),\n      );\n      // Direct classes: \".\": \"className\"\n    } else if (isDirectClass(property)) {\n      res = asArray(value as DirectClass);\n    } else if (isMediaQuery(property)) {\n      res = handleMediaQuery(sheet, value as Styles, property, selector);\n\n      // \"--\": { \"--variable\": \"value\" }\n    } else if (isCssVariables(property)) {\n      res = cssVariablesBlock(sheet, value as CSSVariablesObject, selector);\n\n      // \"property\": \"value\"\n    } else if (isValidProperty(property, value)) {\n      const rule = selector.createRule(property, value);\n      sheet.addRule(rule);\n      output.add(rule.hash);\n    }\n\n    return addEachClass(res, output);\n  });\n\n  return output;\n}\n\nfunction addEachClass(list: string[] | Set<string>, to: Set<string>) {\n  list.forEach((className) => to.add(className));\n  return to;\n}\n\n// eslint-disable-next-line max-statements\nfunction cssVariablesBlock(\n  sheet: Sheet,\n  styles: CSSVariablesObject,\n  selector: Selector,\n) {\n  const classes: ClassSet = new Set<string>();\n\n  const chunkRows: string[] = [];\n  forIn(styles, (property: string, value) => {\n    if (isValidProperty(property, value)) {\n      chunkRows.push(Rule.genRule(property, value));\n      return;\n    }\n    const res = iterateStyles(sheet, value ?? {}, selector);\n    addEachClass(res, classes);\n  });\n\n  if (!selector.scopeClassName) {\n    return classes;\n  }\n\n  if (chunkRows.length) {\n    const output = chunkRows.join(' ');\n    sheet.append(\n      `${mergeSelectors(selector.preconditions, {\n        right: selector.scopeClassName,\n      })} {${output}}`,\n    );\n  }\n\n  classes.add(selector.scopeClassName);\n  return classes;\n}\n\nfunction handleMediaQuery(\n  sheet: Sheet,\n  styles: Styles,\n  mediaQuery: string,\n  selector: Selector,\n) {\n  sheet.append(mediaQuery + ' {');\n\n  // iterateStyles will internally append each rule to the sheet\n  // as needed. All we have to do is just open the block and close it after.\n  const output = iterateStyles(sheet, styles, selector);\n\n  sheet.append('}');\n\n  return output;\n}\n", "export function asArray<T>(v: T | T[]): T[] {\n  return [].concat(v as unknown as []);\n}\n", "import { ClassName } from '../types.js';\n\nexport function isPsuedoSelector(selector: string): boolean {\n  return selector.startsWith(':');\n}\n\nexport function isStyleCondition(selector: string): boolean {\n  return (\n    isString(selector) &&\n    (selector === '*' ||\n      (selector.length > 1 && ':>~.+*'.includes(selector.slice(0, 1))) ||\n      isImmediatePostcondition(selector))\n  );\n}\n\nexport function isValidProperty(\n  property: string,\n  value: unknown,\n): value is string {\n  return (\n    (isString(value) || typeof value === 'number') &&\n    !isCssVariables(property) &&\n    !isPsuedoSelector(property) &&\n    !isMediaQuery(property)\n  );\n}\n\nexport function isMediaQuery(selector: string): boolean {\n  return selector.startsWith('@media');\n}\n\nexport function isDirectClass(selector: string): boolean {\n  return selector === '.';\n}\n\nexport function isCssVariables(selector: string): boolean {\n  return selector === '--';\n}\n\nexport function isString(value: unknown): value is string {\n  return value + '' === value;\n}\n\nexport function isClassName(value: unknown): value is ClassName {\n  return isString(value) && value.length > 1 && value.startsWith('.');\n}\n\nexport function isImmediatePostcondition(\n  value: unknown,\n): value is `&${string}` {\n  return isString(value) && (value.startsWith('&') || isPsuedoSelector(value));\n}\n", "export function joinTruthy(arr: unknown[], delimiter: string = ''): string {\n  return arr.filter(Boolean).join(delimiter);\n}\n", "// Stable hash function.\nexport function stableHash(prefix: string, seed: string): string {\n  let hash = 0;\n  if (seed.length === 0) return hash.toString();\n  for (let i = 0; i < seed.length; i++) {\n    const char = seed.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32bit integer\n  }\n  return `${prefix ?? 'cl'}_${hash.toString(36)}`;\n}\n", "// Some properties need special handling\nexport function handlePropertyValue(property: string, value: string): string {\n  if (property === 'content') {\n    return `\"${value}\"`;\n  }\n\n  return value;\n}\n\nexport function camelCaseToDash(str: string): string {\n  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\n\nexport function joinedProperty(property: string, value: string): string {\n  return `${property}:${value}`;\n}\n\nexport function toClass(str: string): string {\n  return str ? `.${str}` : '';\n}\n\nexport function appendString(base: string, line: string): string {\n  return base ? `${base}\\n${line}` : line;\n}\n", "import { Sheet } from './Sheet';\nimport { asArray } from './utils/asArray';\nimport { isImmediatePostcondition, isPsuedoSelector } from './utils/is';\nimport { joinTruthy } from './utils/joinTruthy';\nimport { stableHash } from './utils/stableHash';\nimport {\n  camelCaseToDash,\n  handlePropertyValue,\n  joinedProperty,\n  toClass,\n} from './utils/stringManipulators';\n\nexport class Rule {\n  public hash: string;\n  public joined: string;\n  public key: string;\n\n  constructor(\n    private sheet: Sheet,\n    public property: string,\n    public value: string,\n    private selector: Selector,\n  ) {\n    this.property = property;\n    this.value = value;\n    this.joined = joinedProperty(property, value);\n    const joinedConditions = this.selector.preconditions.concat(\n      this.selector.postconditions,\n    );\n    this.hash = this.selector.hasConditions\n      ? (this.selector.scopeClassName as string)\n      : stableHash(this.sheet.name, this.joined);\n    this.key = joinTruthy([this.joined, joinedConditions, this.hash]);\n  }\n\n  public toString(): string {\n    let selectors = mergeSelectors(this.selector.preconditions, {\n      right: this.hash,\n    });\n\n    selectors = mergeSelectors(this.selector.postconditions, {\n      left: selectors,\n    });\n\n    return `${selectors} {${Rule.genRule(this.property, this.value)}}`;\n  }\n\n  static genRule(property: string, value: string): string {\n    const transformedProperty = camelCaseToDash(property);\n    return (\n      joinedProperty(\n        transformedProperty,\n        handlePropertyValue(property, value),\n      ) + ';'\n    );\n  }\n}\n\nexport function mergeSelectors(\n  selectors: string[],\n  { left = '', right = '' }: { left?: string; right?: string } = {},\n): string {\n  const output = selectors.reduce((selectors, current) => {\n    if (isPsuedoSelector(current)) {\n      return selectors + current;\n    }\n\n    if (isImmediatePostcondition(current)) {\n      return selectors + current.slice(1);\n    }\n\n    return joinTruthy([selectors, current], ' ');\n\n    // selector then postcondition\n  }, left);\n\n  // preconditions, then selector\n  return joinTruthy([output, toClass(right)], ' ');\n}\n\nexport class Selector {\n  public preconditions: string[] = [];\n  public scopeClassName: string | null = null;\n  public scopeName: string | null = null;\n  public postconditions: string[] = [];\n\n  constructor(\n    private sheet: Sheet,\n    scopeName: string | null = null,\n    {\n      preconditions,\n      postconditions,\n    }: {\n      preconditions?: string[] | string | undefined;\n      postconditions?: string[] | string | undefined;\n    } = {},\n  ) {\n    this.preconditions = preconditions ? asArray(preconditions) : [];\n    this.postconditions = postconditions ? asArray(postconditions) : [];\n    this.setScope(scopeName);\n  }\n\n  private setScope(scopeName: string | null): Selector {\n    if (!scopeName) {\n      return this;\n    }\n\n    if (!this.scopeClassName) {\n      this.scopeName = scopeName;\n      this.scopeClassName = stableHash(\n        this.sheet.name,\n        // adding the count guarantees uniqueness across style.create calls\n        scopeName + this.sheet.count,\n      );\n    }\n\n    return this;\n  }\n\n  get hasConditions(): boolean {\n    return this.preconditions.length > 0 || this.postconditions.length > 0;\n  }\n\n  addScope(scopeName: string): Selector {\n    return new Selector(this.sheet, scopeName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions,\n    });\n  }\n\n  addPrecondition(precondition: string): Selector {\n    return new Selector(this.sheet, this.scopeClassName, {\n      postconditions: this.postconditions,\n      preconditions: this.preconditions.concat(precondition),\n    });\n  }\n\n  addPostcondition(postcondition: string): Selector {\n    return new Selector(this.sheet, this.scopeClassName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions.concat(postcondition),\n    });\n  }\n\n  createRule(property: string, value: string): Rule {\n    return new Rule(this.sheet, property, value, this);\n  }\n}\n", "import { Rule } from './Rule.js';\nimport { StoredStyles } from './types.js';\nimport { isString } from './utils/is.js';\nimport { appendString } from './utils/stringManipulators.js';\n\nexport class Sheet {\n  private styleTag: HTMLStyleElement | undefined;\n\n  // Hash->css\n  private storedStyles: StoredStyles = {};\n\n  // styles->hash\n  private storedClasses: Record<string, string> = {};\n  private style: string = '';\n  public count = 0;\n  public id: string;\n\n  constructor(\n    public name: string,\n    private rootNode?: HTMLElement | null,\n  ) {\n    this.id = `flairup-${name}`;\n\n    this.styleTag = this.createStyleTag();\n  }\n\n  getStyle(): string {\n    return this.style;\n  }\n\n  append(css: string): void {\n    this.style = appendString(this.style, css);\n  }\n\n  apply(): void {\n    this.count++;\n\n    if (!this.styleTag) {\n      return;\n    }\n\n    this.styleTag.innerHTML = this.style;\n  }\n\n  isApplied(): boolean {\n    return !!this.styleTag;\n  }\n\n  createStyleTag(): HTMLStyleElement | undefined {\n    // check that we're in the browser and have access to the DOM\n    if (\n      typeof document === 'undefined' ||\n      this.isApplied() ||\n      // Explicitly disallow mounting to the DOM\n      this.rootNode === null\n    ) {\n      return this.styleTag;\n    }\n\n    const styleTag = document.createElement('style');\n    styleTag.type = 'text/css';\n    styleTag.id = this.id;\n    (this.rootNode ?? document.head).appendChild(styleTag);\n    return styleTag;\n  }\n\n  addRule(rule: Rule): string {\n    const storedClass = this.storedClasses[rule.key];\n\n    if (isString(storedClass)) {\n      return storedClass;\n    }\n\n    this.storedClasses[rule.key] = rule.hash;\n    this.storedStyles[rule.hash] = [rule.property, rule.value];\n\n    this.append(rule.toString());\n    return rule.hash;\n  }\n}\n", "export function forIn<O extends Record<string, unknown>>(\n  obj: O,\n  fn: (key: string, value: O[string]) => void,\n): void {\n  for (const key in obj) {\n    fn(key.trim(), obj[key]);\n  }\n}\n", "import { joinTruthy } from './utils/joinTruthy';\n\nexport function cx(...args: unknown[]): string {\n  const classes = args.reduce((classes: string[], arg) => {\n    if (arg instanceof Set) {\n      classes.push(...arg);\n    } else if (typeof arg === 'string') {\n      classes.push(arg);\n    } else if (Array.isArray(arg)) {\n      classes.push(cx(...arg));\n    } else if (typeof arg === 'object') {\n      // @ts-expect-error - it is a string\n      Object.entries(arg).forEach(([key, value]) => {\n        if (value) {\n          classes.push(key);\n        }\n      });\n    }\n\n    return classes;\n  }, [] as string[]);\n\n  return joinTruthy(classes, ' ').trim();\n}\n"]}